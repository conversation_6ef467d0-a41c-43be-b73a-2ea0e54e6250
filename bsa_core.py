"""
BSA数据生成器 - 核心业务逻辑模块

现代化重构版本，使用Python 3.13语法特性
"""

import pandas as pd
import requests
import random
import io
import json
from pathlib import Path
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from typing import Iterator, Any


class BSADataEngine:
    """BSA数据引擎 - 整合数据生成、时间序列、验证等核心功能"""

    def __init__(self, config: dict[str, Any]):
        """初始化数据引擎"""
        self.config = config
        self.start_time = datetime.strptime(config["start_time"], "%Y-%m-%d %H:%M:%S")
        self.time_delta = self._create_time_delta()
        
        # 如果启用了固定随机种子，则在初始化时设置一次
        if self.config.get("use_fixed_seed", False) and self.config.get("random_seed") is not None:
            random.seed(self.config["random_seed"])

    def _create_time_delta(self) -> timedelta | relativedelta:
        """创建时间增量对象"""
        interval_value = max(1, self.config["interval_value"])
        match self.config["interval_type"]:
            case "year":
                return relativedelta(years=interval_value)
            case "month":
                return relativedelta(months=interval_value)
            case "day":
                return timedelta(days=interval_value)
            case "hour":
                return timedelta(hours=interval_value)
            case "minute":
                return timedelta(minutes=interval_value)
            case "second":
                return timedelta(seconds=interval_value)
            case _:
                raise ValueError(f"不支持的间隔类型: {self.config['interval_type']}")

    def generate_time_series(self) -> Iterator[str]:
        """生成时间序列"""
        current_time = self.start_time
        for _ in range(self.config["total_count"]):
            yield current_time.strftime("%Y-%m-%d %H:%M:%S")
            current_time += self.time_delta

    def generate_data_values(self, size: int) -> list[float]:
        """生成数据值"""
        min_val = self.config.get("min_value", 596)
        max_val = self.config.get("max_value", 604)
        data_type = self.config.get("data_type", "整数")

        match data_type:
            case "整数":
                return [random.randint(int(min_val), int(max_val)) for _ in range(size)]
            case _:  # 浮点数
                return [random.uniform(min_val, max_val) for _ in range(size)]

    def create_record(self, inspection_time: str) -> dict[str, Any]:
        """创建数据记录"""
        return {
            "inspection_time": inspection_time,
            "measurements": [
                {
                    "id": self.config["test_id"],
                    "data": self.generate_data_values(self.config["size"]),
                }
            ],
            "metadata": {
                "f1": self.config.get("f1", ""),
                "f2": self.config.get("f2", ""),
                "f3": self.config.get("f3", ""),
                "f4": self.config.get("f4", ""),
                "f5": self.config.get("f5", ""),
                "f6": self.config.get("f6", ""),
                "f7": self.config.get("f7", ""),
                "f8": self.config.get("f8", ""),
                "f9": self.config.get("f9", ""),
            },
        }

    def record_to_row(self, record: dict[str, Any]) -> dict[str, Any]:
        """将记录转换为DataFrame行"""
        measurement = record["measurements"][0]
        data_values = measurement["data"]
        metadata = record["metadata"]

        row = {
            "test_id": measurement["id"],
            "test_time": record["inspection_time"],
        }

        # 添加v1~v25列和f1~f9列
        row.update(
            {
                f"v{i}": data_values[i - 1] if i - 1 < len(data_values) else ""
                for i in range(1, 26)
            }
        )
        row.update({f"f{i}": metadata.get(f"f{i}", "") for i in range(1, 10)})

        return row

    def generate_complete_dataset(self) -> tuple[pd.DataFrame, list[dict[str, Any]]]:
        """生成完整数据集"""
        records = []
        rows = []

        for time_str in self.generate_time_series():
            record = self.create_record(time_str)
            records.append(record)
            rows.append(self.record_to_row(record))

        # 创建DataFrame
        df = pd.DataFrame(rows)
        column_order = (
            ["test_id", "test_time"]
            + [f"v{i}" for i in range(1, 26)]
            + [f"f{i}" for i in range(1, 10)]
        )
        df = df[column_order]

        return df, records

    @staticmethod
    def validate_config(config: dict[str, Any]) -> tuple[bool, str]:
        """验证配置参数"""
        try:
            datetime.strptime(config.get("start_time", ""), "%Y-%m-%d %H:%M:%S")
        except ValueError:
            return False, "无效的起始时间格式"

        min_val = config.get("min_value", 0)
        max_val = config.get("max_value", 0)
        if min_val >= max_val:
            return False, "最小值不能大于或等于最大值"

        return True, "配置验证通过"


class BSAExportClient:
    """BSA导出和API客户端 - 整合文件导出和API请求功能"""



    @staticmethod
    def create_file_data(
        df: pd.DataFrame,
        file_format: str,
        json_data: list[dict[str, Any]] | None = None,
    ) -> bytes:
        """创建文件数据"""
        match file_format:
            case "csv":
                return df.to_csv(index=False, encoding="utf-8-sig").encode("utf-8-sig")
            case "xlsx" | "xls":
                buffer = io.BytesIO()
                with pd.ExcelWriter(buffer, engine="openpyxl") as writer:
                    df.to_excel(writer, index=False, sheet_name="数据")
                return buffer.getvalue()
            case "json" if json_data:
                return json.dumps(json_data, indent=2, ensure_ascii=False).encode(
                    "utf-8"
                )
            case _:
                return b""

    @staticmethod
    def send_api_request(
        json_data: list[dict[str, Any]], server_address: str, cookie: str
    ) -> dict[str, Any]:
        """发送API请求 - 基于服务器地址直接构建URL"""
        # 直接构建API URL和请求头
        api_url = f"http://{server_address}/api/v1/dataApi/import"
        origin = f"http://{server_address}"
        referer = f"http://{server_address}/BSA-SPC%20v4.html"

        headers = {
            "accept": "application/json",
            "Content-Type": "application/json",
            "Connection": "keep-alive",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            "Host": server_address,
            "Origin": origin,
            "Referer": referer,
            "Cookie": cookie,
        }

        try:
            response = requests.post(
                api_url, headers=headers, json=json_data, timeout=30
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"API请求失败: {e}")


class BSACookieManager:
    """BSA Cookie管理器 - 处理浏览器Cookie的自动获取"""

    @staticmethod
    def get_chrome_cookies(domain: str) -> str:
        """从Chrome浏览器获取指定域名的Cookie

        Args:
            domain: 目标域名，如 '*************'

        Returns:
            str: 格式化的Cookie字符串，如 'name1=value1; name2=value2'

        Raises:
            Exception: 当获取Cookie失败时抛出异常
        """
        try:
            import browser_cookie3

            # 获取Chrome浏览器的Cookie
            cookiejar = browser_cookie3.chrome(domain_name=domain)

            # 将Cookie转换为字符串格式
            cookie_list = []
            for cookie in cookiejar:
                cookie_list.append(f"{cookie.name}={cookie.value}")

            # 用分号和空格连接
            cookie_string = "; ".join(cookie_list)

            if not cookie_string:
                raise Exception(f"未找到域名 '{domain}' 的Cookie，请确保已在Chrome浏览器中登录该网站")

            return cookie_string

        except ImportError:
            raise Exception("browser_cookie3库未安装，请先安装：pip install browser-cookie3")
        except Exception as e:
            if "未找到域名" in str(e):
                raise e
            else:
                raise Exception(f"获取Cookie失败：{str(e)}")


class BSAConfigManager:
    """BSA配置管理器 - 处理JSON配置文件的读写"""

    CONFIG_FILE = "config.json"

    @classmethod
    def load_config(cls) -> dict[str, Any]:
        """加载配置文件"""
        config_path = Path(cls.CONFIG_FILE)

        if not config_path.exists():
            # 如果配置文件不存在，创建默认配置
            default_config = cls._get_default_config()
            cls.save_config(default_config)
            return default_config

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except Exception as e:
            print(f"配置文件加载失败: {e}")
            # 返回默认配置
            return cls._get_default_config()

    @classmethod
    def save_config(cls, config: dict[str, Any]) -> None:
        """保存配置到文件"""
        try:
            with open(cls.CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"配置文件保存失败: {e}")

    @classmethod
    def _get_default_config(cls) -> dict[str, Any]:
        """获取默认配置"""
        return {
            'test_id': 400,
            'start_date': '2025-01-01',
            'start_time': '08:00:00',
            'interval_value': 1,
            'interval_type': 'minute',
            'data_type': '整数',
            'min_value': '595',
            'max_value': '605',
            'total_count': 100,
            'size': 2,
            'server_address': '*************:8002',
            'cookie': '',
            'f1': '',
            'f2': '',
            'f3': '',
            'f4': '',
            'f5': '',
            'f6': '',
            'f7': '',
            'f8': '',
            'f9': ''
        }
